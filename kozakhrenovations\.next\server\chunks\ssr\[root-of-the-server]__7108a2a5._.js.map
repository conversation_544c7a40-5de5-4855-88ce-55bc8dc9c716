{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Vibe_Coding/Kozak%20Website/kozakhrenovations/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useState } from 'react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  return (\n    <header className=\"bg-white shadow-md sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <div className=\"text-2xl font-heading font-bold text-kozak-charcoal\">\n              Kozak <span className=\"text-kozak-orange\">Home Renovations</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link href=\"/\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Home\n            </Link>\n            <div className=\"relative group\">\n              <Link href=\"/services\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n                Services\n              </Link>\n              <div className=\"absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\n                <div className=\"py-2\">\n                  <Link href=\"/services/kitchen-fitting\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Kitchen Fitting\n                  </Link>\n                  <Link href=\"/services/bathroom-renovations\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Bathroom Renovations\n                  </Link>\n                  <Link href=\"/services/loft-conversions\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    Loft Conversions\n                  </Link>\n                  <Link href=\"/services/external-wall-insulation\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    External Wall Insulation\n                  </Link>\n                  <Link href=\"/services/general-renovations\" className=\"block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50\">\n                    General Renovations\n                  </Link>\n                </div>\n              </div>\n            </div>\n            <Link href=\"/our-work\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Our Work\n            </Link>\n            <Link href=\"/about\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              About\n            </Link>\n            <Link href=\"/testimonials\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Testimonials\n            </Link>\n            <Link href=\"/contact\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Contact\n            </Link>\n            <Link href=\"/blog\" className=\"text-kozak-charcoal hover:text-kozak-orange transition-colors\">\n              Blog\n            </Link>\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Link href=\"/contact\" className=\"btn-primary\">\n              Get Free Quote\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n              <Link href=\"/\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Home\n              </Link>\n              <Link href=\"/services\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Services\n              </Link>\n              <Link href=\"/our-work\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Our Work\n              </Link>\n              <Link href=\"/about\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                About\n              </Link>\n              <Link href=\"/testimonials\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Testimonials\n              </Link>\n              <Link href=\"/contact\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Contact\n              </Link>\n              <Link href=\"/blog\" className=\"block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange\">\n                Blog\n              </Link>\n              <div className=\"px-3 py-2\">\n                <Link href=\"/contact\" className=\"btn-primary block text-center\">\n                  Get Free Quote\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAE7C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,uKAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC;gCAAI,WAAU;;oCAAsD;kDAC7D,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;;;;;;sCAK9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uKAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgE;;;;;;8CAGzF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,uKAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAgE;;;;;;sDAGjG,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,uKAAI;wDAAC,MAAK;wDAA4B,WAAU;kEAA+D;;;;;;kEAGhH,8OAAC,uKAAI;wDAAC,MAAK;wDAAiC,WAAU;kEAA+D;;;;;;kEAGrH,8OAAC,uKAAI;wDAAC,MAAK;wDAA6B,WAAU;kEAA+D;;;;;;kEAGjH,8OAAC,uKAAI;wDAAC,MAAK;wDAAqC,WAAU;kEAA+D;;;;;;kEAGzH,8OAAC,uKAAI;wDAAC,MAAK;wDAAgC,WAAU;kEAA+D;;;;;;;;;;;;;;;;;;;;;;;8CAM1H,8OAAC,uKAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAgE;;;;;;8CAGjG,8OAAC,uKAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAgE;;;;;;8CAG9F,8OAAC,uKAAI;oCAAC,MAAK;oCAAgB,WAAU;8CAAgE;;;;;;8CAGrG,8OAAC,uKAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAgE;;;;;;8CAGhG,8OAAC,uKAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAgE;;;;;;;;;;;;sCAM/F,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uKAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAc;;;;;;;;;;;sCAMhD,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE9B,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAM1E,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uKAAI;gCAAC,MAAK;gCAAI,WAAU;0CAA8D;;;;;;0CAGvF,8OAAC,uKAAI;gCAAC,MAAK;gCAAY,WAAU;0CAA8D;;;;;;0CAG/F,8OAAC,uKAAI;gCAAC,MAAK;gCAAY,WAAU;0CAA8D;;;;;;0CAG/F,8OAAC,uKAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA8D;;;;;;0CAG5F,8OAAC,uKAAI;gCAAC,MAAK;gCAAgB,WAAU;0CAA8D;;;;;;0CAGnG,8OAAC,uKAAI;gCAAC,MAAK;gCAAW,WAAU;0CAA8D;;;;;;0CAG9F,8OAAC,uKAAI;gCAAC,MAAK;gCAAQ,WAAU;0CAA8D;;;;;;0CAG3F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uKAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhF", "debugId": null}}]}