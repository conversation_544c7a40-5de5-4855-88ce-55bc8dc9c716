'use client'

import Link from 'next/link'
import Image from 'next/image'
import { useState } from 'react'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <Image
              src="/kozak-logo.svg"
              alt="Kozak Home Renovations Logo"
              width={50}
              height={50}
              className="w-12 h-12"
            />
            <div className="text-2xl font-heading font-bold text-kozak-charcoal">
              Kozak <span className="text-kozak-orange">Home Renovations</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link href="/" className="text-kozak-charcoal hover:text-kozak-orange transition-colors">
              Home
            </Link>
            <div className="relative group">
              <Link href="/services" className="text-kozak-charcoal hover:text-kozak-orange transition-colors">
                Services
              </Link>
              <div className="absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <div className="py-2">
                  <Link href="/services/kitchen-fitting" className="block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50">
                    Kitchen Fitting
                  </Link>
                  <Link href="/services/bathroom-renovations" className="block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50">
                    Bathroom Renovations
                  </Link>
                  <Link href="/services/loft-conversions" className="block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50">
                    Loft Conversions
                  </Link>
                  <Link href="/services/external-wall-insulation" className="block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50">
                    External Wall Insulation
                  </Link>
                  <Link href="/services/general-renovations" className="block px-4 py-2 text-sm text-kozak-charcoal hover:bg-gray-50">
                    General Renovations
                  </Link>
                </div>
              </div>
            </div>
            <Link href="/our-work" className="text-kozak-charcoal hover:text-kozak-orange transition-colors">
              Our Work
            </Link>
            <Link href="/about" className="text-kozak-charcoal hover:text-kozak-orange transition-colors">
              About
            </Link>
            <Link href="/testimonials" className="text-kozak-charcoal hover:text-kozak-orange transition-colors">
              Testimonials
            </Link>
            <Link href="/contact" className="text-kozak-charcoal hover:text-kozak-orange transition-colors">
              Contact
            </Link>
            <Link href="/blog" className="text-kozak-charcoal hover:text-kozak-orange transition-colors">
              Blog
            </Link>
          </nav>

          {/* CTA Button */}
          <div className="hidden md:block">
            <Link href="/contact" className="btn-primary">
              Get Free Quote
            </Link>
          </div>

          {/* Mobile menu button */}
          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              <Link href="/" className="block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange">
                Home
              </Link>
              <Link href="/services" className="block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange">
                Services
              </Link>
              <Link href="/our-work" className="block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange">
                Our Work
              </Link>
              <Link href="/about" className="block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange">
                About
              </Link>
              <Link href="/testimonials" className="block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange">
                Testimonials
              </Link>
              <Link href="/contact" className="block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange">
                Contact
              </Link>
              <Link href="/blog" className="block px-3 py-2 text-kozak-charcoal hover:text-kozak-orange">
                Blog
              </Link>
              <div className="px-3 py-2">
                <Link href="/contact" className="btn-primary block text-center">
                  Get Free Quote
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
